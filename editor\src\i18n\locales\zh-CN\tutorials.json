{"tutorials": {"title": "教程", "start": "开始教程", "next": "下一步", "previous": "上一步", "skip": "跳过", "finish": "完成", "restart": "重新开始", "close": "关闭", "progress": "进度", "step": "步骤", "of": "共", "completed": "已完成", "inProgress": "进行中", "notStarted": "未开始", "difficulty": {"beginner": "初级", "intermediate": "中级", "advanced": "高级", "expert": "专家"}, "categories": {"basics": "基础操作", "modeling": "建模", "animation": "动画", "materials": "材质", "lighting": "光照", "physics": "物理", "scripting": "脚本", "ui": "用户界面", "optimization": "优化", "collaboration": "协作"}, "basicTutorials": {"gettingStarted": {"title": "入门指南", "description": "学习编辑器的基本操作和界面", "steps": {"welcome": "欢迎使用DL引擎编辑器", "interface": "了解编辑器界面", "navigation": "学习场景导航", "selection": "选择和操作对象", "properties": "编辑对象属性"}}, "sceneCreation": {"title": "创建场景", "description": "学习如何创建和管理场景", "steps": {"newScene": "创建新场景", "addObjects": "添加对象到场景", "hierarchy": "理解场景层次结构", "saveScene": "保存场景"}}, "objectManipulation": {"title": "对象操作", "description": "学习如何操作3D对象", "steps": {"transform": "变换对象", "duplicate": "复制对象", "group": "组织对象", "delete": "删除对象"}}}, "animationTutorials": {"basicAnimation": {"title": "基础动画", "description": "学习创建简单的动画", "steps": {"timeline": "了解时间轴", "keyframes": "设置关键帧", "interpolation": "理解插值", "playback": "播放动画"}}, "characterAnimation": {"title": "角色动画", "description": "学习角色动画制作", "steps": {"rigging": "角色绑定", "skinning": "蒙皮设置", "posing": "姿态调整", "walking": "制作行走动画"}}}, "materialTutorials": {"basicMaterials": {"title": "基础材质", "description": "学习材质的基本概念", "steps": {"create": "创建材质", "properties": "设置材质属性", "textures": "应用纹理", "preview": "预览材质效果"}}, "advancedMaterials": {"title": "高级材质", "description": "学习高级材质技术", "steps": {"pbr": "PBR材质", "nodes": "节点编辑器", "shaders": "自定义着色器", "optimization": "材质优化"}}}, "physicsTutorials": {"basicPhysics": {"title": "基础物理", "description": "学习物理系统的基本使用", "steps": {"rigidbody": "刚体组件", "colliders": "碰撞器", "forces": "施加力", "simulation": "物理模拟"}}, "characterController": {"title": "角色控制器", "description": "学习角色物理控制", "steps": {"setup": "设置角色控制器", "movement": "移动控制", "jumping": "跳跃机制", "collision": "碰撞检测"}}}, "scriptingTutorials": {"introduction": {"title": "脚本入门", "description": "学习脚本编程基础", "steps": {"basics": "脚本基础", "components": "组件系统", "events": "事件处理", "debugging": "调试技巧"}}, "gameLogic": {"title": "游戏逻辑", "description": "实现游戏逻辑", "steps": {"input": "输入处理", "state": "状态管理", "ai": "AI行为", "ui": "UI交互"}}}, "tips": {"title": "提示", "hotkeys": "快捷键", "workflow": "工作流程", "bestPractices": "最佳实践", "troubleshooting": "故障排除"}, "feedback": {"helpful": "这个教程有帮助吗？", "yes": "是的", "no": "不是", "improve": "如何改进这个教程？", "submit": "提交反馈", "thanks": "感谢您的反馈！"}, "errors": {"loadFailed": "加载教程失败", "stepFailed": "执行步骤失败", "validationFailed": "验证失败", "timeout": "操作超时"}, "messages": {"tutorialStarted": "教程已开始", "tutorialCompleted": "教程已完成", "stepCompleted": "步骤已完成", "allTutorialsCompleted": "所有教程已完成", "progressSaved": "进度已保存"}, "materialEditing": {"title": "材质编辑教程", "description": "学习如何创建和编辑材质", "steps": {"introduction": {"title": "材质编辑介绍", "description": "欢迎来到材质编辑教程！在这个教程中，您将学习如何创建和编辑各种类型的材质。我们将从基础的材质概念开始，逐步深入到高级的材质技术。"}, "createMaterial": {"title": "创建新材质", "description": "首先，我们需要创建一个新的材质。在项目面板中右键点击，选择'创建' > '材质'。这将创建一个新的材质资产，您可以对其进行编辑。"}, "selectMaterialType": {"title": "选择材质类型", "description": "选择适合您需求的材质类型。常见的类型包括标准材质、PBR材质、无光材质等。每种类型都有不同的属性和用途。"}, "setBasicProperties": {"title": "设置基本属性", "description": "设置材质的基本属性，如颜色、金属度、粗糙度等。这些属性决定了材质的基本外观和光照反应。"}, "addTextures": {"title": "添加纹理", "description": "为材质添加纹理贴图。您可以拖拽纹理文件到相应的纹理槽中，如漫反射贴图、法线贴图、粗糙度贴图等。"}, "adjustTextureSettings": {"title": "调整纹理设置", "description": "调整纹理的设置，如平铺、偏移、旋转等。这些设置可以帮助您获得理想的纹理效果。"}, "setShaderVariants": {"title": "设置着色器变体", "description": "根据需要启用或禁用着色器变体，如透明度、双面渲染、顶点颜色等。这些选项可以改变材质的渲染行为。"}, "previewMaterial": {"title": "预览材质", "description": "在材质预览窗口中查看材质效果。您可以使用不同的预览模型和光照环境来测试材质的外观。"}, "applyToObject": {"title": "应用到对象", "description": "将创建的材质应用到场景中的对象上。您可以通过拖拽材质到对象上，或者在对象的材质属性中选择材质。"}, "saveMaterial": {"title": "保存材质", "description": "保存您的材质设置。点击保存按钮或使用快捷键Ctrl+S来保存材质资产，确保您的工作不会丢失。"}, "completion": {"title": "教程完成", "description": "恭喜您完成了材质编辑教程！您现在已经掌握了创建材质、设置属性、添加纹理等核心技能。继续练习和探索，您将能够创建出更加精美和复杂的材质效果。"}}}, "animationSystem": {"title": "动画系统教程", "description": "学习如何创建和编辑动画", "steps": {"introduction": {"title": "动画系统介绍", "description": "欢迎来到动画系统教程！在这个教程中，您将学习如何使用编辑器的动画系统创建精美的动画。我们将从基础的动画概念开始，逐步深入到高级的动画技术。"}, "openAnimationEditor": {"title": "打开动画编辑器", "description": "首先，我们需要打开动画编辑器。点击菜单栏中的'窗口'选项，然后选择'动画编辑器'。这将打开一个专门用于创建和编辑动画的工作区。"}, "importAnimationModel": {"title": "导入动画模型", "description": "要创建动画，我们首先需要一个3D模型。点击'导入'按钮，选择一个带有骨骼的3D模型文件。确保模型已经正确绑定了骨骼系统。"}, "checkSkeleton": {"title": "检查骨骼结构", "description": "导入模型后，我们需要检查骨骼结构是否正确。在骨骼视图中，您可以看到模型的骨骼层次结构。确保所有骨骼都正确连接并且命名清晰。"}, "createAnimationClip": {"title": "创建动画片段", "description": "现在我们来创建第一个动画片段。点击'创建片段'按钮，为您的动画片段命名。动画片段是动画的基本单位，包含了一系列关键帧数据。"}, "setKeyframes": {"title": "设置关键帧", "description": "关键帧定义了动画在特定时间点的状态。在时间轴上选择一个时间点，然后调整模型的姿态，系统会自动创建关键帧。重复这个过程来创建动画序列。"}, "editCurves": {"title": "编辑动画曲线", "description": "动画曲线控制着关键帧之间的插值方式。在曲线编辑器中，您可以调整曲线的形状来改变动画的感觉，比如让动作更加流畅或者更有弹性。"}, "previewAnimation": {"title": "预览动画", "description": "创建了基本的动画后，点击预览按钮来查看动画效果。您可以调整播放速度，循环播放，或者逐帧查看动画。"}, "createStateMachine": {"title": "创建动画状态机", "description": "状态机允许您管理多个动画片段之间的转换。点击'状态机'标签页，这里您可以创建不同的动画状态，比如待机、行走、跑步等。"}, "addStates": {"title": "添加动画状态", "description": "在状态机中添加新的状态。每个状态代表一个特定的动画片段或行为。点击'添加状态'按钮，然后为状态分配相应的动画片段。"}, "createTransitions": {"title": "创建状态转换", "description": "状态之间需要转换来实现流畅的动画切换。在状态机画布中，从一个状态拖拽到另一个状态来创建转换连接。"}, "setTransitionConditions": {"title": "设置转换条件", "description": "为转换设置触发条件。在转换检查器中，您可以设置参数条件，比如速度大于某个值时从待机转换到行走状态。"}, "createBlendTree": {"title": "创建混合树", "description": "混合树允许您根据参数值混合多个动画。这对于创建方向性动画（如8方向移动）或者根据速度混合不同的动画非常有用。"}, "addBlendParameters": {"title": "添加混合参数", "description": "在参数面板中添加混合参数，比如速度、方向等。这些参数将控制动画的混合权重，实现更加自然的动画效果。"}, "testAnimationSystem": {"title": "测试动画系统", "description": "现在让我们测试整个动画系统。点击测试按钮，尝试改变不同的参数值，观察动画状态的变化和转换效果。"}, "saveAnimationAssets": {"title": "保存动画资产", "description": "完成动画制作后，记得保存您的工作。点击保存按钮，将动画片段、状态机和相关设置保存为资产文件，以便在项目中使用。"}, "completion": {"title": "教程完成", "description": "恭喜您完成了动画系统教程！您现在已经掌握了创建动画片段、设置状态机、使用混合树等核心技能。继续练习和探索，您将能够创建出更加复杂和精美的动画效果。"}}}}}