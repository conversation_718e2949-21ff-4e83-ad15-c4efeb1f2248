/**
 * 视觉脚本教程
 * 引导用户学习如何使用视觉脚本系统
 */
import { Tutorial } from '../services/TutorialService';
import i18n from '../i18n';

export const VisualScriptingTutorial: Tutorial = {
  id: 'visual-scripting',
  title: i18n.t('tutorials.visualScripting.title'),
  description: i18n.t('tutorials.visualScripting.description'),
  category: 'scripting',
  difficulty: 'intermediate',
  duration: 20,
  prerequisites: ['editor-basics'],
  tags: ['脚本', '视觉脚本', '节点', '逻辑'],
  steps: [
    // 步骤1：介绍
    {
      id: 'introduction',
      title: i18n.t('tutorials.visualScripting.steps.introduction.title'),
      description: i18n.t('tutorials.visualScripting.steps.introduction.description'),
      nextButtonText: i18n.t('tutorials.next'),
      skipButtonText: i18n.t('tutorials.skip')
    },
    
    // 步骤2：打开视觉脚本编辑器
    {
      id: 'open-visual-script-editor',
      title: i18n.t('tutorials.visualScripting.steps.openEditor.title'),
      description: i18n.t('tutorials.visualScripting.steps.openEditor.description'),
      targetElement: 'menu-window',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'visual-script-editor-opened',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤3：创建新脚本
    {
      id: 'create-new-script',
      title: i18n.t('tutorials.visualScripting.steps.createNewScript.title'),
      description: i18n.t('tutorials.visualScripting.steps.createNewScript.description'),
      targetElement: 'new-script-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'new-script-created',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤4：了解界面
    {
      id: 'understand-interface',
      title: i18n.t('tutorials.visualScripting.steps.understandInterface.title'),
      description: i18n.t('tutorials.visualScripting.steps.understandInterface.description'),
      targetElement: 'visual-script-canvas',
      highlightElement: true,
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤5：添加事件节点
    {
      id: 'add-event-node',
      title: i18n.t('tutorials.visualScripting.steps.addEventNode.title'),
      description: i18n.t('tutorials.visualScripting.steps.addEventNode.description'),
      targetElement: 'node-library',
      highlightElement: true,
      action: 'drag-drop',
      completionCriteria: 'event-node-added',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤6：添加变量节点
    {
      id: 'add-variable-node',
      title: i18n.t('tutorials.visualScripting.steps.addVariableNode.title'),
      description: i18n.t('tutorials.visualScripting.steps.addVariableNode.description'),
      targetElement: 'variables-panel',
      highlightElement: true,
      action: 'click-drag',
      completionCriteria: 'variable-node-added',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤7：添加逻辑节点
    {
      id: 'add-logic-node',
      title: i18n.t('tutorials.visualScripting.steps.addLogicNode.title'),
      description: i18n.t('tutorials.visualScripting.steps.addLogicNode.description'),
      targetElement: 'node-library-logic',
      highlightElement: true,
      action: 'drag-drop',
      completionCriteria: 'logic-node-added',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤8：连接节点
    {
      id: 'connect-nodes',
      title: i18n.t('tutorials.visualScripting.steps.connectNodes.title'),
      description: i18n.t('tutorials.visualScripting.steps.connectNodes.description'),
      targetElement: 'node-ports',
      highlightElement: true,
      action: 'drag',
      completionCriteria: 'nodes-connected',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤9：添加动作节点
    {
      id: 'add-action-node',
      title: i18n.t('tutorials.visualScripting.steps.addActionNode.title'),
      description: i18n.t('tutorials.visualScripting.steps.addActionNode.description'),
      targetElement: 'node-library-actions',
      highlightElement: true,
      action: 'drag-drop',
      completionCriteria: 'action-node-added',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤10：配置节点属性
    {
      id: 'configure-node-properties',
      title: i18n.t('tutorials.visualScripting.steps.configureNodeProperties.title'),
      description: i18n.t('tutorials.visualScripting.steps.configureNodeProperties.description'),
      targetElement: 'node-inspector',
      highlightElement: true,
      action: 'edit',
      completionCriteria: 'properties-configured',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤11：创建子图
    {
      id: 'create-subgraph',
      title: i18n.t('tutorials.visualScripting.steps.createSubgraph.title'),
      description: i18n.t('tutorials.visualScripting.steps.createSubgraph.description'),
      targetElement: 'subgraph-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'subgraph-created',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤12：添加注释
    {
      id: 'add-comments',
      title: i18n.t('tutorials.visualScripting.steps.addComments.title'),
      description: i18n.t('tutorials.visualScripting.steps.addComments.description'),
      targetElement: 'comment-button',
      highlightElement: true,
      action: 'click-drag',
      completionCriteria: 'comments-added',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤13：调试脚本
    {
      id: 'debug-script',
      title: i18n.t('tutorials.visualScripting.steps.debugScript.title'),
      description: i18n.t('tutorials.visualScripting.steps.debugScript.description'),
      targetElement: 'debug-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'debug-started',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤14：保存脚本
    {
      id: 'save-script',
      title: i18n.t('tutorials.visualScripting.steps.saveScript.title'),
      description: i18n.t('tutorials.visualScripting.steps.saveScript.description'),
      targetElement: 'save-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'script-saved',
      nextButtonText: i18n.t('tutorials.next'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤15：附加到对象
    {
      id: 'attach-to-object',
      title: i18n.t('tutorials.visualScripting.steps.attachToObject.title'),
      description: i18n.t('tutorials.visualScripting.steps.attachToObject.description'),
      targetElement: 'hierarchy-panel',
      highlightElement: true,
      action: 'drag-drop',
      completionCriteria: 'script-attached',
      nextButtonText: i18n.t('tutorials.finish'),
      previousButtonText: i18n.t('tutorials.previous')
    },
    
    // 步骤16：完成
    {
      id: 'completion',
      title: i18n.t('tutorials.visualScripting.steps.completion.title'),
      description: i18n.t('tutorials.visualScripting.steps.completion.description'),
      nextButtonText: i18n.t('tutorials.close'),
      previousButtonText: i18n.t('tutorials.previous')
    }
  ]
};

export default VisualScriptingTutorial;
